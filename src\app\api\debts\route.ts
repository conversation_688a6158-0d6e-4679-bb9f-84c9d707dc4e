import { NextRequest } from 'next/server'

import {
  successResponse,
  errorResponse,
  with<PERSON>rror<PERSON>andler,
  handleDatabaseError,
  parsePaginationParams,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all customer debts with pagination and filtering
export const GET = with<PERSON>rror<PERSON>and<PERSON>(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const { page, limit, offset } = parsePaginationParams(searchParams)

  // Optional filters
  const search = searchParams.get('search')
  const customerName = searchParams.get('customer_name')
  const customerFamilyName = searchParams.get('customer_family_name')
  const productName = searchParams.get('product_name')
  const dateFrom = searchParams.get('date_from')
  const dateTo = searchParams.get('date_to')

  let query = supabase
    .from('customer_debts')
    .select('*', { count: 'exact' })
    .order('debt_date', { ascending: false })
    .range(offset, offset + limit - 1)

  // Apply search filter
  if (search) {
    query = query.or(`customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%,product_name.ilike.%${search}%`)
  }

  // Apply specific filters
  if (customerName) {
    query = query.eq('customer_name', customerName)
  }
  if (customerFamilyName) {
    query = query.eq('customer_family_name', customerFamilyName)
  }
  if (productName) {
    query = query.ilike('product_name', `%${productName}%`)
  }
  if (dateFrom) {
    query = query.gte('debt_date', dateFrom)
  }
  if (dateTo) {
    query = query.lte('debt_date', dateTo)
  }

  const { data: debts, error, count } = await query

  if (error) {
    return handleDatabaseError(error)
  }

  return successResponse({
    debts,
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  })
})

// POST - Create new customer debt
export const POST = withErrorHandler(async (request: NextRequest) => {
  const body = await request.json()

  // Validate required fields
  if (!body.customer_name || !body.customer_family_name || !body.product_name || !body.product_price || !body.quantity) {
    return errorResponse('Missing required fields', 400)
  }

  const {
    customer_name,
    customer_family_name,
    product_name,
    product_price,
    quantity,
    debt_date,
    notes
  } = body

  // Validate numeric fields
  if (typeof product_price !== 'number' || product_price <= 0) {
    return errorResponse('Product price must be a positive number', 400)
  }

  if (typeof quantity !== 'number' || quantity <= 0 || !Number.isInteger(quantity)) {
    return errorResponse('Quantity must be a positive integer', 400)
  }

  // Validate string lengths
  if (customer_name.length < 2 || customer_name.length > 255) {
    return errorResponse('Customer name must be between 2 and 255 characters', 400)
  }

  if (customer_family_name.length < 2 || customer_family_name.length > 255) {
    return errorResponse('Customer family name must be between 2 and 255 characters', 400)
  }

  if (product_name.length < 2 || product_name.length > 255) {
    return errorResponse('Product name must be between 2 and 255 characters', 400)
  }

  // Validate debt date if provided
  if (debt_date && isNaN(Date.parse(debt_date))) {
    return errorResponse('Invalid debt date format', 400)
  }

  // Ensure customer profile exists - create if it doesn't
  const trimmedCustomerName = customer_name.trim()
  const trimmedFamilyName = customer_family_name.trim()

  // Check if customer profile exists
  const { data: existingCustomer } = await supabase
    .from('customers')
    .select('id')
    .eq('customer_name', trimmedCustomerName)
    .eq('customer_family_name', trimmedFamilyName)
    .single()

  // If customer doesn't exist, create the profile
  if (!existingCustomer) {
    const { data: newCustomer, error: customerError } = await supabase
      .from('customers')
      .insert([
        {
          customer_name: trimmedCustomerName,
          customer_family_name: trimmedFamilyName,
          notes: 'Auto-created profile for debt customer'
        }
      ])
      .select()
      .single()

    if (customerError) {
      console.error('Error creating customer profile:', customerError)
      // Continue with debt creation even if customer profile creation fails
    }
  }

  // Create the debt record
  const { data: debt, error } = await supabase
    .from('customer_debts')
    .insert([
      {
        customer_name: trimmedCustomerName,
        customer_family_name: trimmedFamilyName,
        product_name: product_name.trim(),
        product_price: Number(product_price),
        quantity: Number(quantity),
        debt_date: debt_date || new Date().toISOString().split('T')[0],
        notes: notes?.trim() || null
      }
    ])
    .select()
    .single()

  if (error) {
    return handleDatabaseError(error)
  }

  return successResponse({ debt }, 'Debt created successfully', 201)
})
